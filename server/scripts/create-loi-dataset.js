const fs = require('fs').promises;
const path = require('path');
require('dotenv').config({ path: path.join(__dirname, '..', '..', '.env') });
const supabase = require('../supabaseClient');

async function createLoIDataset() {
  try {
    // Read the CSV file
    const csvPath = path.join(__dirname, '../data/dataset_9d39a752-e14e-40fb-8cf5-11c1d3c8338a.csv');
    const csvContent = await fs.readFile(csvPath, 'utf8');

    // Create dataset entry
    const datasetToInsert = {
      name: 'LoI Scoring Dataset',
      description: 'Dataset for LoI (Letter of Intent) scoring evaluation with competency analysis',
      data: {
        csvData: csvContent,
        type: 'loi_scoring'
      },
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    };

    const { data: newDataset, error: insertError } = await supabase
      .from('datasets')
      .insert([datasetToInsert])
      .select('*')
      .single();

    if (insertError) {
      console.error('Error inserting LoI dataset:', insertError);
      return;
    }

    console.log('LoI dataset created successfully:', newDataset.id);
  } catch (error) {
    console.error('Error creating LoI dataset:', error);
  }
}

createLoIDataset();
